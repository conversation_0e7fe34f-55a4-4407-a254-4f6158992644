import React from 'react';

function App() {
  return (
    <div style={{
      padding: '20px',
      fontFamily: 'Arial, sans-serif',
      minHeight: '100vh',
      backgroundColor: '#f5f5f5'
    }}>
      <header style={{
        backgroundColor: '#1890ff',
        color: 'white',
        padding: '20px',
        borderRadius: '8px',
        marginBottom: '20px'
      }}>
        <h1 style={{ margin: 0, fontSize: '24px' }}>IPTV Dashboard</h1>
        <p style={{ margin: '5px 0 0 0', opacity: 0.9 }}>
          Panel de administración completo para servidores IPTV
        </p>
      </header>

      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '20px',
        marginBottom: '20px'
      }}>
        <div style={{
          backgroundColor: 'white',
          padding: '20px',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        }}>
          <h3 style={{ color: '#1890ff', marginTop: 0 }}>👥 Gestión de Usuarios</h3>
          <p>Administrar usuarios, líneas, dispositivos MAG y Enigma</p>
          <button style={{
            backgroundColor: '#1890ff',
            color: 'white',
            border: 'none',
            padding: '8px 16px',
            borderRadius: '4px',
            cursor: 'pointer'
          }}>
            Ver Usuarios
          </button>
        </div>

        <div style={{
          backgroundColor: 'white',
          padding: '20px',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        }}>
          <h3 style={{ color: '#52c41a', marginTop: 0 }}>📺 Gestión de Contenido</h3>
          <p>Streams, canales, películas, series y categorías</p>
          <button style={{
            backgroundColor: '#52c41a',
            color: 'white',
            border: 'none',
            padding: '8px 16px',
            borderRadius: '4px',
            cursor: 'pointer'
          }}>
            Ver Streams
          </button>
        </div>

        <div style={{
          backgroundColor: 'white',
          padding: '20px',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        }}>
          <h3 style={{ color: '#722ed1', marginTop: 0 }}>📊 Monitoreo</h3>
          <p>Conexiones en vivo, logs y estadísticas del servidor</p>
          <button style={{
            backgroundColor: '#722ed1',
            color: 'white',
            border: 'none',
            padding: '8px 16px',
            borderRadius: '4px',
            cursor: 'pointer'
          }}>
            Ver Estadísticas
          </button>
        </div>

        <div style={{
          backgroundColor: 'white',
          padding: '20px',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        }}>
          <h3 style={{ color: '#fa8c16', marginTop: 0 }}>⚙️ Configuración</h3>
          <p>Configuración del sistema, servidores y seguridad</p>
          <button style={{
            backgroundColor: '#fa8c16',
            color: 'white',
            border: 'none',
            padding: '8px 16px',
            borderRadius: '4px',
            cursor: 'pointer'
          }}>
            Configurar
          </button>
        </div>
      </div>

      <div style={{
        backgroundColor: 'white',
        padding: '20px',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <h3 style={{ color: '#1890ff', marginTop: 0 }}>🚀 APIs Soportadas</h3>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '10px' }}>
          <div>
            <strong>GET INFO API:</strong>
            <ul style={{ fontSize: '14px', margin: '5px 0' }}>
              <li>mysql_query, user_info</li>
              <li>get_lines, get_mags, get_enigmas</li>
              <li>get_users, get_streams</li>
            </ul>
          </div>
          <div>
            <strong>LOGS & EVENT API:</strong>
            <ul style={{ fontSize: '14px', margin: '5px 0' }}>
              <li>activity_logs, live_connections</li>
              <li>credit_logs, user_logs</li>
              <li>stream_errors, system_logs</li>
            </ul>
          </div>
          <div>
            <strong>CRUD APIs:</strong>
            <ul style={{ fontSize: '14px', margin: '5px 0' }}>
              <li>LINE API (8 endpoints)</li>
              <li>USER API (6 endpoints)</li>
              <li>MAG API (9 endpoints)</li>
            </ul>
          </div>
          <div>
            <strong>CONFIGURATION APIs:</strong>
            <ul style={{ fontSize: '14px', margin: '5px 0' }}>
              <li>BOUQUET, ACCESS CODE</li>
              <li>HMAC, EPG, GROUPS</li>
              <li>PACKAGES, SERVERS</li>
            </ul>
          </div>
        </div>
      </div>

      <footer style={{
        textAlign: 'center',
        marginTop: '40px',
        padding: '20px',
        color: '#666',
        borderTop: '1px solid #e8e8e8'
      }}>
        <p>Dashboard IPTV completo con soporte para 100+ APIs</p>
        <p style={{ fontSize: '14px' }}>
          Construido con React, TypeScript, Ant Design y Tailwind CSS
        </p>
      </footer>
    </div>
  );
}

export default App;
