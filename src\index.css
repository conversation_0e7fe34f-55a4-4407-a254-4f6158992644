@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: 'Inter', system-ui, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  min-height: 100vh;
  background-color: #f8fafc;
}

.dark body {
  background-color: #0f172a;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.dark ::-webkit-scrollbar-thumb {
  background: #475569;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Ant Design customizations */
.ant-layout {
  min-height: 100vh;
}

.ant-menu-dark {
  background: #1e293b !important;
}

.ant-menu-dark .ant-menu-item-selected {
  background-color: #3b82f6 !important;
}

.ant-table-thead > tr > th {
  background: #f1f5f9 !important;
  border-bottom: 1px solid #e2e8f0 !important;
}

.dark .ant-table-thead > tr > th {
  background: #334155 !important;
  border-bottom: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}
